{"name": "vue-bi-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.38", "pinia": "^2.1.7", "vue-router": "^4.2.5", "chart.js": "^4.4.0", "vue-chartjs": "^5.2.0", "gridstack": "^10.1.2", "papaparse": "^5.4.1", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.3", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@types/papaparse": "^5.3.14"}}