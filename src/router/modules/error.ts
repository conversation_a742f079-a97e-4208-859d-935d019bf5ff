import type { RouteRecordRaw } from 'vue-router'

const errorRoutes: RouteRecordRaw[] = [
  {
    path: '/404',
    name: 'NotFoundPage',
    component: () => import('@/pages/error/NotFound.vue'),
    meta: {
      title: 'Page Not Found',
      description: 'The requested page could not be found',
      module: 'error',
      hidden: true
    }
  },
  // TODO: Tạo các pages này sau
  // {
  //   path: '/500',
  //   name: 'ServerError',
  //   component: () => import('@/pages/error/ServerError.vue'),
  //   meta: {
  //     title: 'Server Error',
  //     description: 'Internal server error occurred',
  //     module: 'error',
  //     hidden: true
  //   }
  // },
  // {
  //   path: '/403',
  //   name: 'Forbidden',
  //   component: () => import('@/pages/error/Forbidden.vue'),
  //   meta: {
  //     title: 'Access Forbidden',
  //     description: 'You do not have permission to access this resource',
  //     module: 'error',
  //     hidden: true
  //   }
  // },
  // Catch-all route for 404 - phải đặt cuối cùng
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/404'
  }
]

export default errorRoutes
