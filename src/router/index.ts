import { createRouter, createWebHistory } from 'vue-router'
import { allRoutes } from './modules'

// Export router utilities and modules for use in components
export * from './modules'
export * from './utils'

export const router = createRouter({
  history: createWebHistory(),
  routes: allRoutes,
  scrollBehavior(to, _from, savedPosition) {
    // Nếu có saved position (back/forward), sử dụng nó
    if (savedPosition) {
      return savedPosition
    }
    // Nếu có hash, scroll đến element đó
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }
    // Mặc định scroll lên top
    return { top: 0 }
  }
})

// Navigation guards
router.beforeEach((to, _from, next) => {
  // Set page title
  if (to.meta?.title) {
    document.title = `${to.meta.title} - BI Dashboard`
  } else {
    document.title = 'BI Dashboard'
  }

  // Add loading state if needed
  // store.commit('setLoading', true)

  next()
})

router.afterEach((to, _from) => {
  // Remove loading state
  // store.commit('setLoading', false)

  // Analytics tracking
  if (typeof (window as any).gtag !== 'undefined') {
    (window as any).gtag('config', 'GA_MEASUREMENT_ID', {
      page_title: to.meta?.title || to.name,
      page_location: window.location.href
    })
  }
})