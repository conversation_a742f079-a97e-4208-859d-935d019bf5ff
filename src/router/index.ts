import { createRouter, createWebHistory } from 'vue-router'

// Dashboard Pages
import Home from '@/pages/dashboard/Home.vue'
import DashboardStore from '@/pages/dashboard/DashboardStore.vue'
import QuickDashboard from '@/pages/dashboard/QuickDashboard.vue'

// Data Pages
import DataSources from '@/pages/data/DataSources.vue'

// Chart Pages
import ChartList from '@/pages/charts/ChartList.vue'
import ChartCreate from '@/pages/charts/ChartCreate.vue'

// Design Pages
import TemplateDesigner from '@/pages/design/TemplateDesigner.vue'

// Settings Pages
import Settings from '@/pages/settings/Settings.vue'

const routes = [
  // Dashboard Routes
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: 'Dashboard Home',
      description: 'Main dashboard overview'
    }
  },
  {
    path: '/dashboards',
    name: 'DashboardStore',
    component: DashboardStore,
    meta: {
      title: 'Dashboard Store',
      description: 'Browse and manage dashboards'
    }
  },
  {
    path: '/dashboard-store',
    name: 'DashboardStoreAlias',
    redirect: '/dashboards'
  },
  {
    path: '/quick-dashboard',
    name: 'QuickDashboard',
    component: QuickDashboard,
    meta: {
      title: 'Quick Dashboard',
      description: 'Create dashboards quickly'
    }
  },

  // Data Routes
  {
    path: '/data-sources',
    name: 'DataSources',
    component: DataSources,
    meta: {
      title: 'Data Sources',
      description: 'Manage your data sources'
    }
  },

  // Chart Routes
  {
    path: '/charts',
    name: 'ChartList',
    component: ChartList,
    meta: {
      title: 'Charts',
      description: 'Manage your charts'
    }
  },
  {
    path: '/charts/create',
    name: 'ChartCreate',
    component: ChartCreate,
    meta: {
      title: 'Create Chart',
      description: 'Create a new chart'
    }
  },

  // Design Routes
  {
    path: '/template-designer',
    name: 'TemplateDesigner',
    component: TemplateDesigner,
    meta: {
      title: 'Template Designer',
      description: 'Design dashboard templates'
    }
  },

  // Settings Routes
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: 'Settings',
      description: 'Application settings and preferences'
    }
  },

  // Catch-all route for 404
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/pages/error/NotFound.vue'),
    meta: {
      title: 'Page Not Found'
    }
  }
]

export const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // Nếu có saved position (back/forward), sử dụng nó
    if (savedPosition) {
      return savedPosition
    }
    // Nếu có hash, scroll đến element đó
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }
    // Mặc định scroll lên top
    return { top: 0 }
  }
})

// Navigation guards
router.beforeEach((to, from, next) => {
  // Set page title
  if (to.meta?.title) {
    document.title = `${to.meta.title} - BI Dashboard`
  } else {
    document.title = 'BI Dashboard'
  }

  // Add loading state if needed
  // store.commit('setLoading', true)

  next()
})

router.afterEach((to, from) => {
  // Remove loading state
  // store.commit('setLoading', false)

  // Analytics tracking
  if (typeof gtag !== 'undefined') {
    gtag('config', 'GA_MEASUREMENT_ID', {
      page_title: to.meta?.title || to.name,
      page_location: window.location.href
    })
  }
})