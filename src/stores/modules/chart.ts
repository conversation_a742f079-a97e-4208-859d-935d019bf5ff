/**
 * Chart Store Module
 * Pinia store cho Chart management
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { chartService } from '@/services/chartService'
import type { 
  Chart, 
  ChartData, 
  ChartConfig,
  ChartTemplate,
  ChartType
} from '@/types/chart'

export const useChartStore = defineStore('chart', () => {
  // State
  const charts = ref<Chart[]>([])
  const currentChart = ref<Chart | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const templates = ref<ChartTemplate[]>([])
  const chartData = ref<Record<string, ChartData>>({})

  // Getters
  const getChartById = computed(() => {
    return (id: string) => charts.value.find(c => c.id === id)
  })

  const chartsByType = computed(() => {
    return (type: ChartType) => charts.value.filter(c => c.type === type)
  })

  const chartsByDataSource = computed(() => {
    return (dataSourceId: string) => charts.value.filter(c => c.dataSourceId === dataSourceId)
  })

  const getChartData = computed(() => {
    return (id: string) => chartData.value[id]
  })

  const templatesByCategory = computed(() => {
    const grouped: Record<string, ChartTemplate[]> = {}
    templates.value.forEach(template => {
      if (!grouped[template.category]) {
        grouped[template.category] = []
      }
      grouped[template.category].push(template)
    })
    return grouped
  })

  // Actions
  const loadCharts = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      charts.value = await chartService.getCharts()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load charts'
      console.error('Error loading charts:', err)
    } finally {
      isLoading.value = false
    }
  }

  const loadChart = async (id: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      currentChart.value = await chartService.getChartById(id)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load chart'
      console.error('Error loading chart:', err)
    } finally {
      isLoading.value = false
    }
  }

  const createChart = async (chart: Omit<Chart, 'id' | 'createdAt'>) => {
    isLoading.value = true
    error.value = null
    
    try {
      const newChart = await chartService.createChart(chart)
      charts.value.push(newChart)
      return newChart
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create chart'
      console.error('Error creating chart:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateChart = async (id: string, updates: Partial<Chart>) => {
    isLoading.value = true
    error.value = null
    
    try {
      const updatedChart = await chartService.updateChart(id, updates)
      
      // Update in charts array
      const index = charts.value.findIndex(c => c.id === id)
      if (index > -1) {
        charts.value[index] = updatedChart
      }
      
      // Update current chart if it's the same
      if (currentChart.value?.id === id) {
        currentChart.value = updatedChart
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update chart'
      console.error('Error updating chart:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteChart = async (id: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      await chartService.deleteChart(id)
      
      // Remove from charts array
      charts.value = charts.value.filter(c => c.id !== id)
      
      // Clear current chart if it's the deleted one
      if (currentChart.value?.id === id) {
        currentChart.value = null
      }
      
      // Remove chart data
      delete chartData.value[id]
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete chart'
      console.error('Error deleting chart:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const loadChartData = async (id: string, filters?: Record<string, any>) => {
    try {
      const data = await chartService.getChartData(id, filters)
      chartData.value[id] = data
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load chart data'
      console.error('Error loading chart data:', err)
      throw err
    }
  }

  const refreshChartData = async (id: string) => {
    try {
      const data = await chartService.refreshChartData(id)
      chartData.value[id] = data
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to refresh chart data'
      console.error('Error refreshing chart data:', err)
      throw err
    }
  }

  const exportChart = async (id: string, format: 'png' | 'jpg' | 'svg' | 'pdf') => {
    try {
      return await chartService.exportChart(id, format)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to export chart'
      console.error('Error exporting chart:', err)
      throw err
    }
  }

  const duplicateChart = async (id: string, name?: string) => {
    try {
      const duplicated = await chartService.duplicateChart(id, name)
      charts.value.push(duplicated)
      return duplicated
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to duplicate chart'
      console.error('Error duplicating chart:', err)
      throw err
    }
  }

  const validateConfig = async (config: ChartConfig) => {
    try {
      return await chartService.validateConfig(config)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to validate config'
      console.error('Error validating config:', err)
      return {
        isValid: false,
        errors: ['Validation failed']
      }
    }
  }

  const loadTemplates = async () => {
    try {
      templates.value = await chartService.getTemplates()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load templates'
      console.error('Error loading templates:', err)
    }
  }

  const createFromTemplate = async (templateId: string, config: Partial<ChartConfig>) => {
    isLoading.value = true
    error.value = null
    
    try {
      const newChart = await chartService.createFromTemplate(templateId, config)
      charts.value.push(newChart)
      return newChart
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create chart from template'
      console.error('Error creating chart from template:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const previewChart = async (config: ChartConfig, dataSourceId: string) => {
    try {
      return await chartService.previewChart(config, dataSourceId)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to preview chart'
      console.error('Error previewing chart:', err)
      throw err
    }
  }

  const clearChartData = (id: string) => {
    delete chartData.value[id]
  }

  const clearAllChartData = () => {
    chartData.value = {}
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    charts,
    currentChart,
    isLoading,
    error,
    templates,
    chartData,
    
    // Getters
    getChartById,
    chartsByType,
    chartsByDataSource,
    getChartData,
    templatesByCategory,
    
    // Actions
    loadCharts,
    loadChart,
    createChart,
    updateChart,
    deleteChart,
    loadChartData,
    refreshChartData,
    exportChart,
    duplicateChart,
    validateConfig,
    loadTemplates,
    createFromTemplate,
    previewChart,
    clearChartData,
    clearAllChartData,
    clearError
  }
})
