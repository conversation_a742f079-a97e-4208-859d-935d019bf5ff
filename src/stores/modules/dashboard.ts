/**
 * Dashboard Store Module
 * Pinia store cho Dashboard management
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { dashboardService } from '@/services/dashboardService'
import type { 
  Dashboard, 
  DashboardWidget, 
  DashboardFilter,
  DashboardExportOptions,
  DashboardShareSettings 
} from '@/types/dashboard'

export const useDashboardStore = defineStore('dashboard', () => {
  // State
  const dashboards = ref<Dashboard[]>([])
  const currentDashboard = ref<Dashboard | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const filters = ref<DashboardFilter[]>([])
  const selectedWidgets = ref<string[]>([])

  // Getters
  const getDashboardById = computed(() => {
    return (id: string) => dashboards.value.find(d => d.id === id)
  })

  const filteredDashboards = computed(() => {
    return dashboards.value.filter(dashboard => {
      // Apply filters logic here
      return true
    })
  })

  const currentDashboardWidgets = computed(() => {
    return currentDashboard.value?.widgets || []
  })

  const hasUnsavedChanges = computed(() => {
    // Logic to detect unsaved changes
    return false
  })

  // Actions
  const loadDashboards = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      dashboards.value = await dashboardService.getDashboards()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load dashboards'
      console.error('Error loading dashboards:', err)
    } finally {
      isLoading.value = false
    }
  }

  const loadDashboard = async (id: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      currentDashboard.value = await dashboardService.getDashboardById(id)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load dashboard'
      console.error('Error loading dashboard:', err)
    } finally {
      isLoading.value = false
    }
  }

  const createDashboard = async (dashboard: Omit<Dashboard, 'id' | 'createdAt'>) => {
    isLoading.value = true
    error.value = null
    
    try {
      const newDashboard = await dashboardService.createDashboard(dashboard)
      dashboards.value.push(newDashboard)
      return newDashboard
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create dashboard'
      console.error('Error creating dashboard:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateDashboard = async (id: string, updates: Partial<Dashboard>) => {
    isLoading.value = true
    error.value = null
    
    try {
      const updatedDashboard = await dashboardService.updateDashboard(id, updates)
      
      // Update in dashboards array
      const index = dashboards.value.findIndex(d => d.id === id)
      if (index > -1) {
        dashboards.value[index] = updatedDashboard
      }
      
      // Update current dashboard if it's the same
      if (currentDashboard.value?.id === id) {
        currentDashboard.value = updatedDashboard
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update dashboard'
      console.error('Error updating dashboard:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteDashboard = async (id: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      await dashboardService.deleteDashboard(id)
      
      // Remove from dashboards array
      dashboards.value = dashboards.value.filter(d => d.id !== id)
      
      // Clear current dashboard if it's the deleted one
      if (currentDashboard.value?.id === id) {
        currentDashboard.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete dashboard'
      console.error('Error deleting dashboard:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const addWidget = async (dashboardId: string, widget: Omit<DashboardWidget, 'id'>) => {
    try {
      const newWidget = await dashboardService.addWidget(dashboardId, widget)
      
      // Update dashboard in array
      const dashboard = dashboards.value.find(d => d.id === dashboardId)
      if (dashboard) {
        dashboard.widgets.push(newWidget)
      }
      
      // Update current dashboard if it's the same
      if (currentDashboard.value?.id === dashboardId) {
        currentDashboard.value.widgets.push(newWidget)
      }
      
      return newWidget
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to add widget'
      console.error('Error adding widget:', err)
      throw err
    }
  }

  const updateWidget = async (
    dashboardId: string, 
    widgetId: string, 
    updates: Partial<DashboardWidget>
  ) => {
    try {
      const updatedWidget = await dashboardService.updateWidget(dashboardId, widgetId, updates)
      
      // Update widget in dashboard
      const dashboard = dashboards.value.find(d => d.id === dashboardId)
      if (dashboard) {
        const widgetIndex = dashboard.widgets.findIndex(w => w.id === widgetId)
        if (widgetIndex > -1) {
          dashboard.widgets[widgetIndex] = updatedWidget
        }
      }
      
      // Update current dashboard if it's the same
      if (currentDashboard.value?.id === dashboardId) {
        const widgetIndex = currentDashboard.value.widgets.findIndex(w => w.id === widgetId)
        if (widgetIndex > -1) {
          currentDashboard.value.widgets[widgetIndex] = updatedWidget
        }
      }
      
      return updatedWidget
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update widget'
      console.error('Error updating widget:', err)
      throw err
    }
  }

  const removeWidget = async (dashboardId: string, widgetId: string) => {
    try {
      await dashboardService.removeWidget(dashboardId, widgetId)
      
      // Remove widget from dashboard
      const dashboard = dashboards.value.find(d => d.id === dashboardId)
      if (dashboard) {
        dashboard.widgets = dashboard.widgets.filter(w => w.id !== widgetId)
      }
      
      // Remove from current dashboard if it's the same
      if (currentDashboard.value?.id === dashboardId) {
        currentDashboard.value.widgets = currentDashboard.value.widgets.filter(w => w.id !== widgetId)
      }
      
      // Remove from selected widgets
      selectedWidgets.value = selectedWidgets.value.filter(id => id !== widgetId)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to remove widget'
      console.error('Error removing widget:', err)
      throw err
    }
  }

  const applyFilter = (filter: DashboardFilter) => {
    const existingIndex = filters.value.findIndex(f => f.id === filter.id)
    if (existingIndex > -1) {
      filters.value[existingIndex] = filter
    } else {
      filters.value.push(filter)
    }
  }

  const removeFilter = (filterId: string) => {
    filters.value = filters.value.filter(f => f.id !== filterId)
  }

  const clearFilters = () => {
    filters.value = []
  }

  const selectWidget = (widgetId: string) => {
    if (!selectedWidgets.value.includes(widgetId)) {
      selectedWidgets.value.push(widgetId)
    }
  }

  const deselectWidget = (widgetId: string) => {
    selectedWidgets.value = selectedWidgets.value.filter(id => id !== widgetId)
  }

  const clearSelection = () => {
    selectedWidgets.value = []
  }

  const exportDashboard = async (id: string, options: DashboardExportOptions) => {
    try {
      return await dashboardService.exportDashboard(id, options)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to export dashboard'
      console.error('Error exporting dashboard:', err)
      throw err
    }
  }

  const shareDashboard = async (id: string, settings: DashboardShareSettings) => {
    try {
      return await dashboardService.shareDashboard(id, settings)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to share dashboard'
      console.error('Error sharing dashboard:', err)
      throw err
    }
  }

  const duplicateDashboard = async (id: string, name?: string) => {
    try {
      const duplicated = await dashboardService.duplicateDashboard(id, name)
      dashboards.value.push(duplicated)
      return duplicated
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to duplicate dashboard'
      console.error('Error duplicating dashboard:', err)
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    dashboards,
    currentDashboard,
    isLoading,
    error,
    filters,
    selectedWidgets,
    
    // Getters
    getDashboardById,
    filteredDashboards,
    currentDashboardWidgets,
    hasUnsavedChanges,
    
    // Actions
    loadDashboards,
    loadDashboard,
    createDashboard,
    updateDashboard,
    deleteDashboard,
    addWidget,
    updateWidget,
    removeWidget,
    applyFilter,
    removeFilter,
    clearFilters,
    selectWidget,
    deselectWidget,
    clearSelection,
    exportDashboard,
    shareDashboard,
    duplicateDashboard,
    clearError
  }
})
